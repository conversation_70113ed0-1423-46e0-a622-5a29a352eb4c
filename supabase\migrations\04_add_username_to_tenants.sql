-- Add username field to tenants table
ALTER TABLE tenants ADD COLUMN username TEXT UNIQUE;

-- Create index for faster username lookups
CREATE INDEX IF NOT EXISTS idx_tenants_username ON tenants(username);

-- Add constraint to ensure username is lowercase and alphanumeric with underscores/hyphens
ALTER TABLE tenants ADD CONSTRAINT username_format CHECK (
  username IS NULL OR 
  (username ~ '^[a-z0-9_-]+$' AND length(username) >= 3 AND length(username) <= 50)
);

-- Update existing tenants with a default username based on their name
-- This is a one-time migration script
UPDATE tenants 
SET username = LOWER(REGEXP_REPLACE(REGEXP_REPLACE(name, '[^a-zA-Z0-9]', '_', 'g'), '_+', '_', 'g'))
WHERE username IS NULL;

-- Make sure usernames are unique by appending numbers if needed
DO $$
DECLARE
    tenant_record RECORD;
    base_username TEXT;
    final_username TEXT;
    counter INTEGER;
BEGIN
    FOR tenant_record IN SELECT id, name FROM tenants WHERE username IS NULL OR username = '' LOOP
        -- Generate base username from tenant name
        base_username := LOWER(REGEXP_REPLACE(REGEXP_REPLACE(tenant_record.name, '[^a-zA-Z0-9]', '_', 'g'), '_+', '_', 'g'));
        base_username := TRIM(BOTH '_' FROM base_username);
        
        -- Ensure minimum length
        IF LENGTH(base_username) < 3 THEN
            base_username := base_username || '_tenant';
        END IF;
        
        -- Ensure maximum length
        IF LENGTH(base_username) > 45 THEN
            base_username := LEFT(base_username, 45);
        END IF;
        
        final_username := base_username;
        counter := 1;
        
        -- Check if username exists and increment counter if needed
        WHILE EXISTS (SELECT 1 FROM tenants WHERE username = final_username AND id != tenant_record.id) LOOP
            final_username := base_username || '_' || counter;
            counter := counter + 1;
            
            -- Prevent infinite loop
            IF counter > 1000 THEN
                final_username := base_username || '_' || EXTRACT(EPOCH FROM NOW())::INTEGER;
                EXIT;
            END IF;
        END LOOP;
        
        -- Update the tenant with the unique username
        UPDATE tenants SET username = final_username WHERE id = tenant_record.id;
    END LOOP;
END $$;
