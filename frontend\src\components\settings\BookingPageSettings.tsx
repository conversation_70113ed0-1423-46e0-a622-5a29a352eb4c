import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { api } from '../../utils/api';
import { showApiError } from '../../utils/errorHandling';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs';
import { Switch } from '../ui/Switch';
import { FeatureGate, FeatureError } from '../subscription/FeatureGate';
import { useSubscription } from '../../hooks/useSubscription';
import { ColorPicker } from '../ui/ColorPicker';
import { toast } from 'react-hot-toast';

const BookingPageSettings: React.FC = () => {
  const { user } = useAuth();
  const tenantId = user?.tenantId;
  const { isFeatureAvailable } = useSubscription();
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<any>(null);
  const [settings, setSettings] = useState<any>({
    title: 'Book an Appointment',
    description: 'Select a service and time to book your appointment.',
    primary_color: '#3b82f6',
    show_logo: true,
    custom_css: '',
    custom_js: '',
    custom_confirmation_message: '',
    require_phone: true,
    require_address: false,
    show_prices: true,
    show_duration: true,
    allow_cancellation: true,
    cancellation_period_hours: 24
  });

  const [bookingUrl, setBookingUrl] = useState<string>('');
  const [customBookingUrl, setCustomBookingUrl] = useState<string>('');
  const [defaultBookingUrl, setDefaultBookingUrl] = useState<string>('');
  const [embedCode, setEmbedCode] = useState<string>('');
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  // Determine if user has access to advanced customization
  const hasCustomizableBookingPage = isFeatureAvailable('customizableBookingPage');

  // Fetch booking page settings
  useEffect(() => {
    const fetchSettings = async () => {
      if (!tenantId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await api.get(`/booking-pages/${tenantId}/settings`);
        
        if (response.data.success) {
          setSettings(response.data.data);
        }

        // Fetch booking URL
        const urlResponse = await api.get(`/booking-pages/${tenantId}/url`);
        console.log('URL Response:', urlResponse.data);
        if (urlResponse.data.success) {
          setBookingUrl(urlResponse.data.data.bookingUrl);
          setCustomBookingUrl(urlResponse.data.data.customBookingUrl || '');
          setDefaultBookingUrl(urlResponse.data.data.defaultBookingUrl);
          setEmbedCode(urlResponse.data.data.embedCode);
          setQrCodeUrl(urlResponse.data.data.qrCodeUrl);
          console.log('Set booking URL:', urlResponse.data.data.bookingUrl);
        }

        setError(null);
      } catch (err) {
        console.error('Error fetching booking page settings:', err);
        setError(err);

        // Set fallback URL even if API fails
        if (tenantId) {
          const fallbackUrl = `${window.location.origin}/booking/${tenantId}`;
          setBookingUrl(fallbackUrl);
          setDefaultBookingUrl(fallbackUrl);
          setEmbedCode(`<iframe src="${fallbackUrl}" width="100%" height="600" frameborder="0"></iframe>`);
          setQrCodeUrl(`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(fallbackUrl)}`);
          console.log('Set fallback booking URL:', fallbackUrl);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [tenantId]);

  // Save booking page settings
  const saveSettings = async () => {
    try {
      setSaving(true);
      console.log('Saving settings:', settings);
      const response = await api.put(`/booking-pages/${tenantId}/settings`, settings);
      console.log('Save response:', response.data);

      if (response.data.success) {
        toast.success('Booking page settings saved successfully');
        // Refresh the settings to get the updated data
        const updatedResponse = await api.get(`/booking-pages/${tenantId}/settings`);
        if (updatedResponse.data.success) {
          setSettings(updatedResponse.data.data);
        }
      } else {
        toast.error(response.data.message || 'Failed to save booking page settings');
      }
    } catch (err) {
      console.error('Error saving settings:', err);
      showApiError(err, 'Failed to save booking page settings', (featureError) => {
        setError(featureError);
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle input changes
  const handleChange = (field: string, value: any) => {
    setSettings({
      ...settings,
      [field]: value
    });
  };

  // Preview booking page
  const previewBookingPage = () => {
    if (tenantId) {
      window.open(`/booking/${tenantId}`, '_blank');
    }
  };

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"></div>
      </div>
    );
  }

  if (error && error.upgradeRequired) {
    return <FeatureError error={error} />;
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between space-y-4 sm:flex-row sm:items-center sm:space-y-0">
        <h1 className="text-2xl font-bold">Booking Page Settings</h1>
        
        <div className="flex space-x-3">
          <Button variant="outline" onClick={previewBookingPage}>
            Preview
          </Button>
          <Button onClick={saveSettings} disabled={saving}>
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="appearance">Appearance</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>
        
        <TabsContent value="general" className="pt-4">
          <Card className="p-6">
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Page Title
                </label>
                <input
                  type="text"
                  value={settings.title}
                  onChange={(e) => handleChange('title', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  value={settings.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                  rows={3}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                />
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Form Settings</h3>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Require Phone Number</span>
                  <Switch
                    checked={settings.require_phone}
                    onChange={(checked) => handleChange('require_phone', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Require Address</span>
                  <Switch
                    checked={settings.require_address}
                    onChange={(checked) => handleChange('require_address', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Show Service Prices</span>
                  <Switch
                    checked={settings.show_prices}
                    onChange={(checked) => handleChange('show_prices', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Show Service Duration</span>
                  <Switch
                    checked={settings.show_duration}
                    onChange={(checked) => handleChange('show_duration', checked)}
                  />
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Cancellation Settings</h3>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Allow Clients to Cancel</span>
                  <Switch
                    checked={settings.allow_cancellation}
                    onChange={(checked) => handleChange('allow_cancellation', checked)}
                  />
                </div>
                
                {settings.allow_cancellation && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Cancellation Period (hours before appointment)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={settings.cancellation_period_hours}
                      onChange={(e) => handleChange('cancellation_period_hours', parseInt(e.target.value))}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>
                )}
              </div>
            </div>
          </Card>
        </TabsContent>
        
        <TabsContent value="appearance" className="pt-4">
          <Card className="p-6">
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Primary Color
                </label>
                <div className="mt-1">
                  <ColorPicker
                    color={settings.primary_color}
                    onChange={(color) => handleChange('primary_color', color)}
                  />
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Show Logo</span>
                <Switch
                  checked={settings.show_logo}
                  onChange={(checked) => handleChange('show_logo', checked)}
                />
              </div>
              
              <FeatureGate
                featureId="customizableBookingPage"
                fallback={
                  <div className="rounded-md bg-yellow-50 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-yellow-800">
                          Advanced Customization
                        </h3>
                        <div className="mt-2 text-sm text-yellow-700">
                          <p>
                            Upgrade to Pro or Pro+ to access advanced customization options for your booking page.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                }
              >
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Custom Confirmation Message
                  </label>
                  <textarea
                    value={settings.custom_confirmation_message}
                    onChange={(e) => handleChange('custom_confirmation_message', e.target.value)}
                    rows={3}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    placeholder="Thank you for booking with us! We look forward to seeing you."
                  />
                </div>
              </FeatureGate>
            </div>
          </Card>
        </TabsContent>
        
        <TabsContent value="advanced" className="pt-4">
          <FeatureGate featureId="customizableBookingPage">
            <Card className="p-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Custom CSS
                  </label>
                  <textarea
                    value={settings.custom_css}
                    onChange={(e) => handleChange('custom_css', e.target.value)}
                    rows={8}
                    className="mt-1 block w-full font-mono rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    placeholder="/* Add your custom CSS here */"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Add custom CSS to style your booking page. This will be added to the &lt;style&gt; tag in the page header.
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Custom JavaScript
                  </label>
                  <textarea
                    value={settings.custom_js}
                    onChange={(e) => handleChange('custom_js', e.target.value)}
                    rows={8}
                    className="mt-1 block w-full font-mono rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    placeholder="// Add your custom JavaScript here"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Add custom JavaScript to enhance your booking page. This will be added to the &lt;script&gt; tag at the end of the page.
                  </p>
                </div>
              </div>
            </Card>
          </FeatureGate>
        </TabsContent>
      </Tabs>

      {/* URL Sharing Section */}
      {bookingUrl && (
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">Share Your Booking Page</h3>

          <div className="space-y-4">
            {/* Custom URL if available */}
            {customBookingUrl && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Custom Booking URL (Recommended)
                </label>
                <div className="flex">
                  <input
                    type="text"
                    value={customBookingUrl}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-sm"
                  />
                  <button
                    onClick={() => navigator.clipboard.writeText(customBookingUrl)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 text-sm"
                  >
                    Copy
                  </button>
                </div>
              </div>
            )}

            {/* Default URL */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {customBookingUrl ? 'Default Booking URL' : 'Booking Page URL'}
              </label>
              <div className="flex">
                <input
                  type="text"
                  value={defaultBookingUrl || bookingUrl}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-sm"
                />
                <button
                  onClick={() => navigator.clipboard.writeText(defaultBookingUrl || bookingUrl)}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-r-md hover:bg-gray-300 text-sm"
                >
                  Copy
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Embed Code
              </label>
              <div className="flex">
                <textarea
                  value={embedCode}
                  readOnly
                  rows={2}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-sm font-mono"
                />
                <button
                  onClick={() => navigator.clipboard.writeText(embedCode)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 text-sm"
                >
                  Copy
                </button>
              </div>
            </div>

            {qrCodeUrl && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  QR Code
                </label>
                <div className="flex items-center space-x-4">
                  <img src={qrCodeUrl} alt="QR Code" className="w-24 h-24" />
                  <div className="text-sm text-gray-600">
                    <p>Scan this QR code to access your booking page</p>
                    <a
                      href={qrCodeUrl}
                      download="booking-qr-code.png"
                      className="text-blue-600 hover:text-blue-800"
                    >
                      Download QR Code
                    </a>
                  </div>
                </div>
              </div>
            )}

            {customBookingUrl && (
              <p className="text-sm text-gray-500">
                Both URLs will work for your booking page. The custom URL is more user-friendly and easier to remember.
              </p>
            )}
          </div>
        </Card>
      )}
    </div>
  );
};

export default BookingPageSettings;
