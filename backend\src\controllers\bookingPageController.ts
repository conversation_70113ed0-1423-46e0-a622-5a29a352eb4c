import { Request, Response } from 'express';
import {
  getPublicBookingPage,
  getBookingPageSettings,
  updateBookingPageSettings,
  canUseCustomizableBookingPage
} from '../services/bookingPageService';
import { UserRole } from '../models/supabase';
import supabase from '../config/supabase';

/**
 * Get public booking page data for a tenant
 * This endpoint is used by the public booking page
 */
export const getPublicBookingPageData = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: 'Tenant ID is required'
      });
    }

    const bookingPageData = await getPublicBookingPage(tenantId);

    if (!bookingPageData) {
      return res.status(404).json({
        success: false,
        message: 'Booking page not found or tenant is inactive'
      });
    }

    // Check if tenant is active
    if (!bookingPageData.tenant.active) {
      return res.status(403).json({
        success: false,
        message: 'This business is not currently accepting bookings'
      });
    }

    // Transform staff services for easier frontend consumption
    const staffWithServices = bookingPageData.staff.map((staffMember: any) => {
      const staffServices = bookingPageData.staffServices
        .filter((ss: any) => ss.staff_id === staffMember.id)
        .map((ss: any) => ss.service_id);
      
      return {
        ...staffMember,
        serviceIds: staffServices
      };
    });

    res.json({
      success: true,
      data: {
        tenant: {
          id: bookingPageData.tenant.id,
          name: bookingPageData.tenant.name,
          logo: bookingPageData.tenant.logo,
          primaryColor: bookingPageData.tenant.primary_color,
          secondaryColor: bookingPageData.tenant.secondary_color,
          welcomeMessage: bookingPageData.tenant.welcome_message,
          currency: bookingPageData.tenant.currency,
          timezone: bookingPageData.tenant.timezone
        },
        settings: bookingPageData.settings,
        services: bookingPageData.services,
        staff: staffWithServices
      }
    });

  } catch (error) {
    console.error('Error in getPublicBookingPageData:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get booking page settings for a tenant (protected route)
 */
export const getTenantBookingPageSettings = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Check if user has access to this tenant
    if (req.user.tenantId !== tenantId && req.user.role !== UserRole.SUPER_ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const settings = await getBookingPageSettings(tenantId);

    if (!settings) {
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch booking page settings'
      });
    }

    // Check if user can use customizable features
    const canUseCustomizable = await canUseCustomizableBookingPage(req.user.id);

    res.json({
      success: true,
      data: settings,
      canUseCustomizable: canUseCustomizable.canUse,
      upgradeMessage: canUseCustomizable.reason
    });

  } catch (error) {
    console.error('Error in getTenantBookingPageSettings:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Update booking page settings for a tenant (protected route)
 */
export const updateTenantBookingPageSettings = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Check if user has admin access to this tenant
    if (req.user.tenantId !== tenantId || req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const {
      title,
      description,
      primary_color,
      show_logo,
      custom_css,
      custom_js,
      custom_confirmation_message,
      require_phone,
      require_address,
      show_prices,
      show_duration,
      allow_cancellation,
      cancellation_period_hours
    } = req.body;

    const settings = {
      title,
      description,
      primary_color,
      show_logo,
      custom_css,
      custom_js,
      custom_confirmation_message,
      require_phone,
      require_address,
      show_prices,
      show_duration,
      allow_cancellation,
      cancellation_period_hours
    };

    const result = await updateBookingPageSettings(tenantId, settings, req.user.id);

    if (!result.success) {
      return res.status(result.upgradeRequired ? 403 : 500).json(result);
    }

    res.json(result);

  } catch (error) {
    console.error('Error in updateTenantBookingPageSettings:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Generate booking page URL for a tenant
 */
export const getBookingPageUrl = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Check if user has access to this tenant
    if (req.user.tenantId !== tenantId && req.user.role !== UserRole.SUPER_ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Get tenant info to check for username
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('username')
      .eq('id', tenantId)
      .single();

    const baseUrl = process.env.FRONTEND_URL || 'https://scheduly-eight.vercel.app';

    // Prefer username-based URL if available
    let bookingUrl = `${baseUrl}/booking/${tenantId}`;
    let customBookingUrl = null;

    if (tenant && tenant.username) {
      customBookingUrl = `${baseUrl}/book/${tenant.username}`;
      bookingUrl = customBookingUrl; // Use custom URL as primary
    }

    res.json({
      success: true,
      data: {
        bookingUrl,
        customBookingUrl,
        defaultBookingUrl: `${baseUrl}/booking/${tenantId}`,
        embedCode: `<iframe src="${bookingUrl}" width="100%" height="600" frameborder="0"></iframe>`,
        qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(bookingUrl)}`
      }
    });

  } catch (error) {
    console.error('Error in getBookingPageUrl:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
