import express, { Request, Response, NextFunction } from 'express';
import {
  getTenant,
  updateTenant,
  getTenantStaff,
  getTenantBookingPage,
  getTenantByUsername
} from '../controllers/tenantController';
import { authenticate, authorize, checkTenantAccess } from '../middleware/auth';
import { UserRole } from '../models/supabase';

const router = express.Router();

// Public routes
router.get('/:tenantId/booking', getTenantBookingPage as any);
router.get('/by-username/:username', getTenantByUsername as any);

// Protected routes
router.get('/:tenantId', authenticate as any, checkTenantAccess as any, getTenant as any);
router.put('/:tenantId', authenticate as any, authorize(UserRole.ADMIN) as any, checkTenantAccess as any, updateTenant as any);
router.get('/:tenantId/staff', authenticate as any, checkTenantAccess as any, getTenantStaff as any);

export default router;
