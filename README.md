# Scheduly - Online Appointment Scheduler

Scheduly is a SaaS application for scheduling and managing appointments across various industries. It provides an intuitive interface that integrates with existing calendar systems to facilitate easy appointment setting, rescheduling, and cancellations.

## Features

- **User Authentication**: Register/login for businesses (tenants) and their clients
- **Calendar Integration**: Sync appointments with Google Calendar
- **Appointment Scheduling**: Book, reschedule, and cancel appointments with staff availability checks
- **Automated Reminders**: Send notifications via email and SMS 24 hours before appointments
- **Customizable Booking Pages**: Allow businesses to personalize branding (logos, colors, messages)
- **Analytics Tools**: Provide insights into appointment trends and staff utilization
- **Multi-Tenancy**: Isolate data for each business using a tenant ID
- **Password Reset**: Secure password reset functionality with email verification
- **Responsive Design**: Mobile-friendly interface for all user types

## Technology Stack

### Backend
- Node.js with Express.js (RESTful API server)
- TypeScript (type safety)
- PostgreSQL with Supabase (replacing Sequelize ORM)
- Serverless deployment on Vercel

### Frontend
- React.js (v19) with TypeScript
- Tailwind CSS for styling
- React Router (v7) for navigation
- React Hook Form for form management

### Authentication
- JWT (JSON Web Tokens) for user authentication
- Supabase Auth for user management
- OAuth 2.0 for Google Calendar integration

### Notifications
- Resend Email API (primary email service)
- Custom domain email sending
- Nodemailer (legacy email support)

## Deployment

The application is deployed using the following infrastructure:

- **Frontend**: Hosted on Vercel
- **Backend API**: Serverless functions on Vercel
- **Database**: Supabase PostgreSQL database
- **Email Service**: Resend with custom domain (scheduly.in)

## Quick Start for Local Development

1. Clone the repository
```bash
git clone https://github.com/TheVishnuVardan/scheduly.git
cd scheduly
```

2. Install dependencies
```bash
# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

3. Configure environment variables
   - Copy `.env.example` to `.env` in both backend and frontend directories
   - Update the values in the `.env` files with your configuration
   - For Supabase, add your project URL and keys
   - For Resend, add your API key and sender domain

4. Set up Supabase
   - Create a new Supabase project
   - Run the migration scripts in the `supabase/migrations` directory
   - Configure CORS settings in Supabase dashboard (see `docs/supabase-cors-config.md`)

5. Start the development servers
```bash
# Start backend server
cd backend
npm run dev

# In a new terminal, start frontend server
cd frontend
npm run dev
```

The backend will be available at http://localhost:5000 and the frontend at http://localhost:5173.

## Documentation

- [DEPLOYMENT.md](docs/DEPLOYMENT.md) - Detailed deployment instructions for Vercel and Supabase
- [DOCUMENTATION.md](docs/DOCUMENTATION.md) - Comprehensive documentation including setup and troubleshooting
- [INTEGRATIONS.md](docs/INTEGRATIONS.md) - Detailed instructions for setting up third-party integrations
- [LOCAL_SETUP.md](docs/LOCAL_SETUP.md) - Guide for setting up local development environment

## Project Structure

```
scheduly/
├── backend/             # Backend API server
│   ├── src/
│   │   ├── config/      # Configuration files
│   │   ├── controllers/ # API controllers
│   │   ├── middleware/  # Express middleware
│   │   ├── models/      # Database models (Supabase types)
│   │   ├── routes/      # API routes
│   │   ├── utils/       # Utility functions
│   │   │   ├── email.ts         # Legacy Nodemailer email utility
│   │   │   ├── resendEmail.ts   # New Resend email utility
│   │   │   ├── scheduler.ts     # Cron job scheduler
│   │   │   └── modelHelpers.ts  # Supabase model helpers
│   │   ├── tests/       # Test scripts
│   │   └── server.ts    # Server entry point
│   ├── fix-all.js        # Script to fix TypeScript errors
│   └── package.json
│
├── database/            # Database-related files
│   └── migrations/      # Database migration scripts
│       ├── rls-policies.sql  # Row Level Security policies
│       └── README.md         # Migration documentation
│
├── docs/                # Documentation files
│   ├── DEPLOYMENT.md    # Deployment instructions
│   ├── DOCUMENTATION.md # Comprehensive documentation
│   ├── INTEGRATIONS.md  # Integration guides
│   ├── LOCAL_SETUP.md   # Local development setup
│   └── supabase-cors-config.md # Supabase CORS configuration
│
├── frontend/            # Frontend React application
│   ├── public/          # Static files
│   ├── src/
│   │   ├── components/  # Reusable components
│   │   ├── contexts/    # React contexts (Auth, Currency, Timezone)
│   │   ├── pages/       # Page components
│   │   │   ├── ForgotPassword.tsx  # Password reset request page
│   │   │   ├── ResetPassword.tsx   # Password reset page
│   │   │   └── ...
│   │   ├── services/    # API services
│   │   ├── App.tsx      # Main App component with routes
│   │   └── main.tsx     # Entry point
│   └── package.json
│
├── scripts/             # Utility scripts
│   ├── generate-jwt-secret.js  # JWT secret generator
│   └── README.md              # Scripts documentation
│
├── src/                 # Source files at root level
│   └── utils/           # Utility files
│       └── modelHelpers.ts  # Model helper utilities
│
├── supabase/            # Supabase configuration
│   └── migrations/      # Initial database migration scripts
│
├── .gitignore           # Git ignore file
├── package.json         # Root package.json
├── README.md            # Project overview
└── vercel.json          # Vercel deployment configuration
```

## Recent Updates

- **Project Organization**: Restructured project for better maintainability and organization
- **Database Migration**: Migrated from Sequelize ORM to Supabase
- **Email Service**: Implemented Resend Email API with custom domain support
- **Password Reset**: Added secure password reset functionality
- **Deployment**: Set up serverless deployment on Vercel
- **Documentation**: Updated comprehensive documentation with deployment and setup guides

## License

This project is licensed under the MIT License - see the LICENSE file for details.
