import { createClient } from '@supabase/supabase-js';
import config from '../config';
import { userHasFeature } from './subscriptionService';

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

// Check if user can use customizable booking page
export async function canUseCustomizableBookingPage(userId: string): Promise<{
  canUse: boolean;
  reason?: string;
}> {
  try {
    const hasFeature = await userHasFeature(userId, 'customizableBookingPage');
    
    if (!hasFeature) {
      return {
        canUse: false,
        reason: 'Customizable booking page requires a Pro or Pro+ subscription'
      };
    }
    
    return { canUse: true };
  } catch (error) {
    console.error('Error in canUseCustomizableBookingPage:', error);
    return {
      canUse: false,
      reason: 'Error checking feature access'
    };
  }
}

// Get booking page settings for a tenant
export async function getBookingPageSettings(tenantId: string) {
  try {
    const { data, error } = await supabase
      .from('booking_pages')
      .select('*')
      .eq('tenant_id', tenantId)
      .single();
    
    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      console.error('Error fetching booking page settings:', error);
      return null;
    }
    
    // Return default settings if none found
    if (!data) {
      return {
        tenant_id: tenantId,
        title: 'Book an Appointment',
        description: 'Select a service and time to book your appointment.',
        primary_color: '#3b82f6',
        show_logo: true,
        custom_css: '',
        custom_js: '',
        custom_confirmation_message: '',
        require_phone: true,
        require_address: false,
        show_prices: true,
        show_duration: true,
        allow_cancellation: true,
        cancellation_period_hours: 24,
        created_at: new Date(),
        updated_at: new Date()
      };
    }
    
    return data;
  } catch (error) {
    console.error('Error in getBookingPageSettings:', error);
    return null;
  }
}

// Update booking page settings with tier-based features
export async function updateBookingPageSettings(tenantId: string, settings: any, userId: string) {
  try {
    // Check if user is trying to update advanced fields
    const hasAdvancedFields = settings.custom_css || 
                             settings.custom_js || 
                             settings.custom_confirmation_message;
    
    if (hasAdvancedFields) {
      // Check if user can use customizable booking page
      const canUseCustomizable = await canUseCustomizableBookingPage(userId);
      
      if (!canUseCustomizable.canUse) {
        return {
          success: false,
          message: canUseCustomizable.reason,
          upgradeRequired: true,
          requiredFeature: 'customizableBookingPage'
        };
      }
    }
    
    // Check if settings already exist
    const { data: existingSettings, error: checkError } = await supabase
      .from('booking_pages')
      .select('id')
      .eq('tenant_id', tenantId)
      .maybeSingle();
    
    let result;
    
    if (existingSettings) {
      // Update existing settings
      const { data, error } = await supabase
        .from('booking_pages')
        .update({
          ...settings,
          updated_at: new Date()
        })
        .eq('tenant_id', tenantId)
        .select()
        .single();
      
      if (error) {
        console.error('Error updating booking page settings:', error);
        return {
          success: false,
          message: 'Failed to update booking page settings'
        };
      }
      
      result = data;
    } else {
      // Create new settings
      const { data, error } = await supabase
        .from('booking_pages')
        .insert({
          tenant_id: tenantId,
          ...settings,
          created_at: new Date(),
          updated_at: new Date()
        })
        .select()
        .single();
      
      if (error) {
        console.error('Error creating booking page settings:', error);
        return {
          success: false,
          message: 'Failed to create booking page settings'
        };
      }
      
      result = data;
    }
    
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error in updateBookingPageSettings:', error);
    return {
      success: false,
      message: 'Internal server error'
    };
  }
}

// Get public booking page data
export async function getPublicBookingPage(tenantId: string) {
  try {
    // Get tenant info
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('*')
      .eq('id', tenantId)
      .single();
    
    if (tenantError) {
      console.error('Error fetching tenant:', tenantError);
      return null;
    }
    
    // Get booking page settings
    const settings = await getBookingPageSettings(tenantId);
    
    // Get services
    const { data: services, error: servicesError } = await supabase
      .from('services')
      .select('*')
      .eq('tenant_id', tenantId)
      .eq('active', true)
      .order('name', { ascending: true });
    
    if (servicesError) {
      console.error('Error fetching services:', servicesError);
      return null;
    }
    
    // Get staff
    const { data: staff, error: staffError } = await supabase
      .from('users')
      .select('id, first_name, last_name, email, phone, avatar_url, bio')
      .eq('tenant_id', tenantId)
      .eq('role', 'staff')
      .eq('active', true)
      .order('first_name', { ascending: true });
    
    if (staffError) {
      console.error('Error fetching staff:', staffError);
      return null;
    }
    
    // Get staff services
    const { data: staffServices, error: staffServicesError } = await supabase
      .from('staff_services')
      .select('staff_id, service_id')
      .eq('tenant_id', tenantId);
    
    if (staffServicesError) {
      console.error('Error fetching staff services:', staffServicesError);
      return null;
    }
    
    return {
      tenant,
      settings,
      services,
      staff,
      staffServices
    };
  } catch (error) {
    console.error('Error in getPublicBookingPage:', error);
    return null;
  }
}
