import { Request, Response } from 'express';
import { UserRole, Tenant as TenantType } from '../models';
import { Tenant, User } from '../utils/modelHelpers';
import { QueryOptions } from '../utils/modelHelpers';

// Get tenant details
export const getTenant = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    const tenant = await Tenant.findByPk(tenantId);

    if (!tenant) {
      return res.status(404).json({ message: 'Tenant not found' });
    }

    res.json({
      id: tenant.id,
      name: tenant.name,
      email: tenant.email,
      phone: tenant.phone,
      address: tenant.address,
      logo: tenant.logo,
      primaryColor: tenant.primaryColor,
      secondaryColor: tenant.secondaryColor,
      welcomeMessage: tenant.welcomeMessage,
      currency: tenant.currency,
      timezone: tenant.timezone,
    });
  } catch (error) {
    console.error('Get tenant error:', error);
    res.status(500).json({ message: 'Failed to get tenant details' });
  }
};

// Update tenant details
export const updateTenant = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.params;
    const {
      name,
      email,
      phone,
      address,
      username,
      logo,
      primaryColor,
      secondaryColor,
      welcomeMessage,
      currency,
      timezone
    } = req.body;

    // Check if user is admin of this tenant
    if (req.user.tenantId !== tenantId || req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to update this tenant' });
    }

    const tenant = await Tenant.findByPk(tenantId);

    if (!tenant) {
      return res.status(404).json({ message: 'Tenant not found' });
    }

    // Update tenant
    const updatedTenant = await Tenant.update(
      {
        name: name || tenant.name,
        email: email || tenant.email,
        phone: phone || tenant.phone,
        address: address || tenant.address,
        username: username !== undefined ? username : tenant.username,
        logo: logo !== undefined ? logo : tenant.logo,
        primaryColor: primaryColor || tenant.primaryColor,
        secondaryColor: secondaryColor || tenant.secondaryColor,
        welcomeMessage: welcomeMessage !== undefined ? welcomeMessage : tenant.welcomeMessage,
        currency: currency || tenant.currency,
        timezone: timezone || tenant.timezone,
      },
      {
        where: {
          id: tenantId
        }
      }
    );

    // Get the updated tenant data
    const refreshedTenant = await Tenant.findByPk(tenantId);

    if (!refreshedTenant) {
      return res.status(404).json({ message: 'Tenant not found after update' });
    }

    res.json({
      message: 'Tenant updated successfully',
      tenant: {
        id: refreshedTenant.id,
        name: refreshedTenant.name,
        email: refreshedTenant.email,
        phone: refreshedTenant.phone,
        address: refreshedTenant.address,
        username: refreshedTenant.username,
        logo: refreshedTenant.logo,
        primaryColor: refreshedTenant.primaryColor,
        secondaryColor: refreshedTenant.secondaryColor,
        welcomeMessage: refreshedTenant.welcomeMessage,
        currency: refreshedTenant.currency,
        timezone: refreshedTenant.timezone,
      },
    });
  } catch (error) {
    console.error('Update tenant error:', error);
    res.status(500).json({ message: 'Failed to update tenant' });
  }
};

// Get tenant staff
export const getTenantStaff = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    const tenant = await Tenant.findByPk(tenantId);

    if (!tenant) {
      return res.status(404).json({ message: 'Tenant not found' });
    }

    const staff = await User.findAll({
      where: {
        tenantId,
        role: [UserRole.ADMIN, UserRole.STAFF],
        active: true,
      },
      attributes: ['id', 'firstName', 'lastName', 'email', 'phone', 'role'],
    });

    res.json(staff);
  } catch (error) {
    console.error('Get tenant staff error:', error);
    res.status(500).json({ message: 'Failed to get tenant staff' });
  }
};

// Get tenant by username
export const getTenantByUsername = async (req: Request, res: Response) => {
  try {
    const { username } = req.params;

    const tenant = await Tenant.findOne({
      where: { username }
    });

    if (!tenant) {
      return res.status(404).json({
        success: false,
        message: 'Tenant not found'
      });
    }

    if (tenant.active === false) {
      return res.status(403).json({
        success: false,
        message: 'This business is not currently accepting bookings'
      });
    }

    res.json({
      success: true,
      data: {
        id: tenant.id,
        name: tenant.name,
        username: tenant.username,
        logo: tenant.logo,
        primaryColor: tenant.primaryColor,
        secondaryColor: tenant.secondaryColor,
        welcomeMessage: tenant.welcomeMessage,
      }
    });
  } catch (error) {
    console.error('Get tenant by username error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tenant by username'
    });
  }
};

// Get tenant booking page
export const getTenantBookingPage = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    const tenant = await Tenant.findByPk(tenantId);

    if (!tenant) {
      return res.status(404).json({ message: 'Tenant not found' });
    }

    if (tenant.active === false) {
      return res.status(403).json({ message: 'This business is not currently accepting bookings' });
    }

    res.json({
      id: tenant.id,
      name: tenant.name,
      logo: tenant.logo,
      primaryColor: tenant.primaryColor,
      secondaryColor: tenant.secondaryColor,
      welcomeMessage: tenant.welcomeMessage,
    });
  } catch (error) {
    console.error('Get tenant booking page error:', error);
    res.status(500).json({ message: 'Failed to get tenant booking page' });
  }
};
