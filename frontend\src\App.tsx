import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { CurrencyProvider } from './contexts/CurrencyContext';
import { TimezoneProvider } from './contexts/TimezoneContext';
import ProtectedRoute from './components/ProtectedRoute';
import RootRedirect from './components/RootRedirect';
import Layout from './components/Layout';
import { UserRole } from './services/userService';

// Pages
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import Dashboard from './pages/Dashboard';
import NotFound from './pages/NotFound';
import Unauthorized from './pages/Unauthorized';
import Profile from './pages/Profile';
import Pricing from './pages/Pricing';
import Contact from './pages/Contact';
import SubscriptionPage from './pages/SubscriptionPage';

// Service Pages
import Services from './pages/Services';
import NewService from './pages/NewService';
import EditService from './pages/EditService';
import ServiceDetail from './pages/ServiceDetail';

// Staff Pages
import Staff from './pages/Staff';
import NewStaff from './pages/NewStaff';
import EditStaff from './pages/EditStaff';
import StaffDetail from './pages/StaffDetail';
import StaffAvailability from './pages/StaffAvailability';

// Appointment Pages
import Appointments from './pages/Appointments';
import AppointmentDetail from './pages/AppointmentDetail';
import NewAppointment from './pages/NewAppointment';
import EditAppointment from './pages/EditAppointment';
import BookingPage from './pages/BookingPage';
import BookingPageSettings from './components/settings/BookingPageSettings';
import MyAppointments from './pages/MyAppointments';
import Settings from './pages/Settings';
import Analytics from './pages/Analytics';
import Clients from './pages/Clients';
import ClientDetail from './pages/ClientDetail';
import ClientForm from './pages/ClientForm';
import GoogleCalendarCallback from './pages/GoogleCalendarCallback';

// Super Admin Pages
import SuperAdminDashboard from './pages/SuperAdminDashboard';
import SuperAdminBusinesses from './pages/SuperAdminBusinesses';
import SuperAdminBusinessDetail from './pages/SuperAdminBusinessDetail';
import SubscriptionManagementPage from './pages/admin/SubscriptionManagementPage';
import SubscriptionAnalyticsPage from './pages/admin/SubscriptionAnalyticsPage';


function App() {
  return (
    <AuthProvider>
      <CurrencyProvider>
        <TimezoneProvider>
          <Router>
          <Routes>
          {/* Public Routes */}
          <Route path="/" element={<Home />} />
          <Route path="/root-redirect" element={<RootRedirect />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/reset-password" element={<ResetPassword />} />
          <Route path="/pricing" element={<Pricing />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/unauthorized" element={<Unauthorized />} />
          <Route path="/booking/:tenantId" element={<BookingPage />} />
          <Route path="/google-calendar-callback" element={<GoogleCalendarCallback />} />

          {/* Protected Routes - All Users */}
          <Route element={<ProtectedRoute />}>
            <Route path="/profile" element={<Profile />} />
          </Route>

          {/* Protected Routes - All Users except Super Admin */}
          <Route
            element={
              <ProtectedRoute
                allowedRoles={[UserRole.ADMIN, UserRole.STAFF, UserRole.CLIENT]}
              />
            }
          >
            <Route path="/subscription" element={<SubscriptionPage />} />
          </Route>

          {/* Protected Routes - Admin & Staff */}
          <Route
            element={
              <ProtectedRoute
                allowedRoles={[UserRole.ADMIN, UserRole.STAFF]}
              />
            }
          >
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/appointments" element={<Appointments />} />
            <Route path="/appointments/new" element={<NewAppointment />} />
            <Route path="/appointments/:id" element={<AppointmentDetail />} />
            <Route path="/appointments/:id/edit" element={<EditAppointment />} />
          </Route>

          {/* Protected Routes - Admin Only */}
          <Route
            element={
              <ProtectedRoute
                allowedRoles={[UserRole.ADMIN]}
              />
            }
          >
            {/* Services Routes */}
            <Route path="/services" element={<Services />} />
            <Route path="/services/new" element={<NewService />} />
            <Route path="/services/:serviceId" element={<ServiceDetail />} />
            <Route path="/services/:serviceId/edit" element={<EditService />} />

            {/* Staff Routes */}
            <Route path="/staff" element={<Staff />} />
            <Route path="/staff/new" element={<NewStaff />} />
            <Route path="/staff/:staffId" element={<StaffDetail />} />
            <Route path="/staff/:staffId/edit" element={<EditStaff />} />
            <Route path="/staff/:staffId/availability" element={<StaffAvailability />} />

            {/* Client Routes */}
            <Route path="/clients" element={<Clients />} />
            <Route path="/clients/new" element={<ClientForm />} />
            <Route path="/clients/:clientId" element={<ClientDetail />} />
            <Route path="/clients/:clientId/edit" element={<ClientForm />} />

            {/* Settings Routes */}
            <Route path="/settings" element={<Settings />} />
            <Route path="/booking-page-settings" element={<Layout><BookingPageSettings /></Layout>} />
            <Route path="/analytics" element={<Analytics />} />
          </Route>

          {/* Protected Routes - Client Only */}
          <Route
            element={
              <ProtectedRoute
                allowedRoles={[UserRole.CLIENT]}
              />
            }
          >
            <Route path="/my-appointments" element={<MyAppointments />} />
            <Route path="/book" element={<NewAppointment />} />
          </Route>

          {/* Protected Routes - Super Admin Only */}
          <Route
            element={
              <ProtectedRoute
                allowedRoles={[UserRole.SUPER_ADMIN]}
              />
            }
          >
            <Route path="/super-admin/dashboard" element={<SuperAdminDashboard />} />
            <Route path="/super-admin/businesses" element={<SuperAdminBusinesses />} />
            <Route path="/super-admin/businesses/:tenantId" element={<SuperAdminBusinessDetail />} />
            <Route path="/admin/subscription" element={<SubscriptionManagementPage />} />
            <Route path="/admin/subscription/analytics" element={<SubscriptionAnalyticsPage />} />
          </Route>

          {/* Catch All */}
          <Route path="*" element={<NotFound />} />
          </Routes>
          </Router>
        </TimezoneProvider>
      </CurrencyProvider>
    </AuthProvider>
  );
}

export default App;


