// Supabase models and types with proper TypeScript definitions
import { supabase } from '../config/supabase';
import { PostgrestFilterBuilder } from '@supabase/postgrest-js';

// Enum types
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  STAFF = 'staff',
  CLIENT = 'client'
}

export enum DayOfWeek {
  MONDAY = 'monday',
  TUESDAY = 'tuesday',
  WEDNESDAY = 'wednesday',
  THURSDAY = 'thursday',
  FRIDAY = 'friday',
  SATURDAY = 'saturday',
  SUNDAY = 'sunday'
}

export enum AppointmentStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  NO_SHOW = 'no_show'
}

export enum NotificationType {
  APPOINTMENT_CREATED = 'appointment_created',
  APPOINTMENT_UPDATED = 'appointment_updated',
  APPOINTMENT_CANCELLED = 'appointment_cancelled',
  APPOINTMENT_REMINDER = 'appointment_reminder'
}

export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  BOTH = 'both'
}

// Database interfaces (snake_case for Supabase)
export interface TenantDB {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  username?: string;
  logo?: string;
  primary_color?: string;
  secondary_color?: string;
  welcome_message?: string;
  currency?: string;
  timezone?: string;
  active: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface UserDB {
  id: string;
  tenant_id?: string;
  first_name: string;
  last_name: string;
  email: string;
  password?: string;
  phone?: string;
  role: UserRole;
  google_id?: string;
  google_calendar_token?: string;
  google_calendar_refresh_token?: string;
  google_calendar_token_expiry?: string; // ISO date string
  reset_token?: string;
  reset_token_expiry?: string; // ISO date string
  active: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  tenant?: TenantDB;
}

export interface ServiceDB {
  id: string;
  tenant_id: string;
  name: string;
  description?: string;
  duration: number;
  price: number;
  color?: string;
  active: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface StaffServiceDB {
  id: string;
  staff_id: string;
  service_id: string;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface AvailabilityDB {
  id: string;
  staff_id: string;
  day_of_week: DayOfWeek;
  start_time: string;
  end_time: string;
  is_available: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface TimeOffDB {
  id: string;
  staff_id: string;
  start_date: string; // ISO date string
  end_date: string; // ISO date string
  reason?: string;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface AppointmentDB {
  id: string;
  tenant_id: string;
  client_id: string;
  staff_id: string;
  service_id: string;
  start_time: string; // ISO date string
  end_time: string; // ISO date string
  status: AppointmentStatus;
  notes?: string;
  google_event_id?: string;
  reminder_sent: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface NotificationDB {
  id: string;
  user_id: string;
  appointment_id?: string;
  type: NotificationType;
  channel: NotificationChannel;
  content: string;
  sent: boolean;
  sent_at?: string; // ISO date string
  error?: string;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

// Application interfaces (camelCase for TypeScript)
export interface Tenant {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  username?: string;
  logo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  welcomeMessage?: string;
  currency?: string;
  timezone?: string;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  tenantId?: string;
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  phone?: string;
  role: UserRole;
  googleId?: string;
  googleCalendarToken?: string;
  googleCalendarRefreshToken?: string;
  googleCalendarTokenExpiry?: Date;
  resetToken?: string;
  resetTokenExpiry?: Date;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
  tenant?: Tenant;
}

export interface Service {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  duration: number;
  price: number;
  color?: string;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface StaffService {
  id: string;
  staffId: string;
  serviceId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Availability {
  id: string;
  staffId: string;
  dayOfWeek: DayOfWeek;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TimeOff {
  id: string;
  staffId: string;
  startDate: Date;
  endDate: Date;
  reason?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Appointment {
  id: string;
  tenantId: string;
  clientId: string;
  staffId: string;
  serviceId: string;
  startTime: Date;
  endTime: Date;
  status: AppointmentStatus;
  notes?: string;
  googleEventId?: string;
  reminderSent: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Notification {
  id: string;
  userId: string;
  appointmentId?: string;
  type: NotificationType;
  channel: NotificationChannel;
  content: string;
  sent: boolean;
  sentAt?: Date;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Export default database object
export default supabase;
