import api from './api';

export interface TenantData {
  name: string;
  email: string;
  phone: string;
  address: string;
  username?: string;
  logo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  welcomeMessage?: string;
  currency?: string;
  timezone?: string;
}

const tenantService = {
  // Get tenant details
  getTenant: async (tenantId: string) => {
    const response = await api.get(`/tenants/${tenantId}`);
    return response.data;
  },

  // Update tenant details
  updateTenant: async (tenantId: string, data: Partial<TenantData>) => {
    const response = await api.put(`/tenants/${tenantId}`, data);
    return response.data;
  },

  // Get tenant staff
  getTenantStaff: async (tenantId: string) => {
    const response = await api.get(`/tenants/${tenantId}/staff`);
    return response.data;
  },

  // Get tenant booking page
  getTenantBookingPage: async (tenantId: string) => {
    const response = await api.get(`/tenants/${tenantId}/booking`);
    return response.data;
  },
};

export default tenantService;
