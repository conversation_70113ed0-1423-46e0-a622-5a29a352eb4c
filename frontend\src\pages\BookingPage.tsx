import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import Layout from '../components/Layout';
import { api } from '../utils/api';
import { useCurrency } from '../contexts/CurrencyContext';

interface Service {
  id: string;
  name: string;
  description?: string;
  duration: number;
  price: number;
  active: boolean;
}

interface Staff {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  services?: Service[];
}

interface Tenant {
  id: string;
  name: string;
  logo?: string;
  primaryColor?: string;
  welcomeMessage?: string;
}

interface BookingPageSettings {
  title: string;
  description: string;
  primary_color: string;
  show_logo: boolean;
  custom_css: string;
  custom_js: string;
  custom_confirmation_message: string;
  require_phone: boolean;
  require_address: boolean;
  show_prices: boolean;
  show_duration: boolean;
  allow_cancellation: boolean;
  cancellation_period_hours: number;
}

interface ClientInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

const BookingPage: React.FC = () => {
  const { tenantId } = useParams<{ tenantId: string }>();
  const { formatPrice } = useCurrency();

  // State
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [bookingSettings, setBookingSettings] = useState<BookingPageSettings | null>(null);
  const [selectedService, setSelectedService] = useState<string>('');
  const [selectedStaff, setSelectedStaff] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [availableTimeSlots, setAvailableTimeSlots] = useState<string[]>([]);
  const [clientInfo, setClientInfo] = useState<ClientInfo>({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  });

  // UI state
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [bookingSuccess, setBookingSuccess] = useState(false);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      if (!tenantId) return;

      try {
        setIsLoading(true);
        setError(null);

        // Fetch tenant info
        const tenantResponse = await api.get(`/tenants/${tenantId}/booking`);
        setTenant(tenantResponse.data);

        // Fetch services
        const servicesResponse = await api.get(`/services/${tenantId}/services`);
        setServices(servicesResponse.data.filter((service: Service) => service.active));

        // Fetch booking page settings
        try {
          const settingsResponse = await api.get(`/booking-pages/${tenantId}/public`);
          if (settingsResponse.data.success) {
            setBookingSettings(settingsResponse.data.data);
          }
        } catch (settingsErr) {
          console.log('No custom booking page settings found, using defaults');
        }

      } catch (err: any) {
        console.error('Error fetching data:', err);
        if (err.response?.status === 403) {
          setError('This business is not currently accepting bookings.');
        } else if (err.response?.status === 404) {
          setError('Booking page not found.');
        } else {
          setError('Failed to load booking page. Please try again later.');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [tenantId]);

  // Fetch time slots when service and date are selected
  useEffect(() => {
    const fetchTimeSlots = async () => {
      if (!tenantId || !selectedService || !selectedDate) return;

      try {
        // First, get staff members who can provide this service
        const staffResponse = await api.get(`/staff/${tenantId}/staff`);
        const availableStaff = staffResponse.data.filter((staffMember: Staff) =>
          staffMember.services?.some(service => service.id === selectedService)
        );

        if (availableStaff.length === 0) {
          console.log('No staff available for this service');
          setAvailableTimeSlots([]);
          return;
        }

        // Use the first available staff member if none is selected
        const staffIdToUse = selectedStaff || availableStaff[0].id;

        const response = await api.get(`/appointments/${tenantId}/timeslots`, {
          params: {
            serviceId: selectedService,
            staffId: staffIdToUse,
            date: selectedDate
          }
        });

        // Extract time strings from the response
        const timeSlots = response.data.timeSlots || [];
        const timeStrings = timeSlots.map((slot: any) => {
          const date = new Date(slot.startTime);
          return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:00`;
        });

        setAvailableTimeSlots(timeStrings);

        // Clear selected time if it's no longer available
        if (selectedTime && !timeStrings.includes(selectedTime)) {
          setSelectedTime('');
        }
      } catch (err: any) {
        console.error('Error fetching time slots:', err);
        setAvailableTimeSlots([]);
      }
    };

    fetchTimeSlots();
  }, [tenantId, selectedService, selectedStaff, selectedDate]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tenantId || !selectedService || !selectedDate || !selectedTime) {
      setError('Please complete all required fields');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      // Create appointment
      const appointmentData = {
        clientId: null, // Will be created by the backend
        staffId: selectedStaff || null,
        serviceId: selectedService,
        startTime: `${selectedDate}T${selectedTime}`,
        notes: 'Booked through public booking page',
        clientInfo: {
          firstName: clientInfo.firstName,
          lastName: clientInfo.lastName,
          email: clientInfo.email,
          phone: clientInfo.phone
        }
      };

      await api.post(`/appointments/${tenantId}/appointments`, appointmentData);
      setBookingSuccess(true);

    } catch (err: any) {
      console.error('Error booking appointment:', err);
      setError(err.response?.data?.message || 'Failed to book appointment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper functions
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const getSelectedService = () => {
    return services.find(service => service.id === selectedService);
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </Layout>
    );
  }

  if (error && !tenant) {
    return (
      <Layout>
        <div className="max-w-3xl mx-auto text-center py-12">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </Layout>
    );
  }

  if (bookingSuccess) {
    return (
      <Layout>
        <div className="max-w-3xl mx-auto text-center py-12">
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <h2 className="text-2xl font-bold mb-4">Appointment Booked Successfully!</h2>
            <p>
              Thank you for booking with {tenant?.name}. You will receive a confirmation email shortly.
            </p>
          </div>
          <button
            onClick={() => {
              setBookingSuccess(false);
              setStep(1);
              setSelectedService('');
              setSelectedStaff('');
              setSelectedTime('');
              setClientInfo({
                firstName: '',
                lastName: '',
                email: '',
                phone: ''
              });
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Book Another Appointment
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-3xl mx-auto">
        {tenant && (
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              {bookingSettings?.title || tenant.name}
            </h1>
            <p className="text-gray-600">
              {bookingSettings?.description || tenant.welcomeMessage || 'Select a service and time to book your appointment.'}
            </p>
          </div>
        )}

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {/* Progress Steps */}
          <div className="flex border-b">
            <div
              className={`flex-1 text-center py-4 ${
                step === 1 ? 'bg-blue-50 text-blue-600 font-medium' : ''
              }`}
            >
              1. Select Service & Time
            </div>
            <div
              className={`flex-1 text-center py-4 ${
                step === 2 ? 'bg-blue-50 text-blue-600 font-medium' : ''
              }`}
            >
              2. Your Information
            </div>
            <div
              className={`flex-1 text-center py-4 ${
                step === 3 ? 'bg-blue-50 text-blue-600 font-medium' : ''
              }`}
            >
              3. Confirm Booking
            </div>
          </div>

          <div className="p-6">
            {/* Step 1: Select Service & Time */}
            {step === 1 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Select Service & Time</h2>

                <div className="mb-4">
                  <label htmlFor="service" className="block text-gray-700 font-medium mb-2">
                    Service *
                  </label>
                  <select
                    id="service"
                    value={selectedService}
                    onChange={(e) => {
                      setSelectedService(e.target.value);
                      setSelectedTime('');
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select a service</option>
                    {services.map((service) => (
                      <option key={service.id} value={service.id}>
                        {service.name}
                        {bookingSettings?.show_prices !== false && ` - ${formatPrice(service.price)}`}
                        {bookingSettings?.show_duration !== false && ` (${service.duration} min)`}
                      </option>
                    ))}
                  </select>
                </div>

                {selectedService && (
                  <div className="mb-4">
                    <label htmlFor="date" className="block text-gray-700 font-medium mb-2">
                      Date *
                    </label>
                    <input
                      type="date"
                      id="date"
                      value={selectedDate}
                      onChange={(e) => {
                        setSelectedDate(e.target.value);
                        setSelectedTime('');
                      }}
                      min={new Date().toISOString().split('T')[0]}
                      max={new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                        .toISOString()
                        .split('T')[0]}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                )}

                {selectedDate && selectedService && (
                  <div className="mb-4">
                    <label className="block text-gray-700 font-medium mb-2">
                      Available Time Slots *
                    </label>
                    {availableTimeSlots.length > 0 ? (
                      <div className="grid grid-cols-3 gap-2">
                        {availableTimeSlots.map((time) => (
                          <button
                            key={time}
                            type="button"
                            className={`py-2 px-4 rounded-md text-center ${
                              selectedTime === time
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-100 hover:bg-gray-200'
                            }`}
                            onClick={() => setSelectedTime(time)}
                          >
                            {formatTime(time)}
                          </button>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4 text-gray-500">
                        No available time slots for the selected date.
                      </div>
                    )}
                  </div>
                )}

                <div className="mt-6 flex justify-end">
                  <button
                    type="button"
                    onClick={() => {
                      if (!selectedService || !selectedDate || !selectedTime) {
                        setError('Please complete all required fields');
                        return;
                      }
                      setError(null);
                      setStep(2);
                    }}
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                    disabled={!selectedService || !selectedDate || !selectedTime}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}

            {/* Step 2: Your Information */}
            {step === 2 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Your Information</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label htmlFor="firstName" className="block text-gray-700 font-medium mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      value={clientInfo.firstName}
                      onChange={(e) => setClientInfo(prev => ({ ...prev, firstName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="lastName" className="block text-gray-700 font-medium mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      value={clientInfo.lastName}
                      onChange={(e) => setClientInfo(prev => ({ ...prev, lastName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={clientInfo.email}
                    onChange={(e) => setClientInfo(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
                    Phone Number{bookingSettings?.require_phone && ' *'}
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    value={clientInfo.phone}
                    onChange={(e) => setClientInfo(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required={bookingSettings?.require_phone}
                  />
                </div>

                <div className="mt-6 flex justify-between">
                  <button
                    type="button"
                    onClick={() => setStep(1)}
                    className="bg-gray-200 text-gray-800 px-6 py-2 rounded-md hover:bg-gray-300"
                  >
                    Back
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      const phoneRequired = bookingSettings?.require_phone;
                      if (!clientInfo.firstName || !clientInfo.lastName || !clientInfo.email || (phoneRequired && !clientInfo.phone)) {
                        setError('Please complete all required fields');
                        return;
                      }
                      setError(null);
                      setStep(3);
                    }}
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                    disabled={!clientInfo.firstName || !clientInfo.lastName || !clientInfo.email || (bookingSettings?.require_phone && !clientInfo.phone)}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}

            {/* Step 3: Confirm Booking */}
            {step === 3 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Confirm Your Booking</h2>

                <div className="bg-gray-50 p-4 rounded-md mb-6">
                  <h3 className="font-medium text-gray-800 mb-2">Appointment Details</h3>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Service</p>
                      <p className="font-medium">{getSelectedService()?.name}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Date</p>
                      <p className="font-medium">
                        {new Date(selectedDate).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Time</p>
                      <p className="font-medium">{formatTime(selectedTime)}</p>
                    </div>

                    {bookingSettings?.show_duration !== false && (
                      <div>
                        <p className="text-sm text-gray-500">Duration</p>
                        <p className="font-medium">{getSelectedService()?.duration} minutes</p>
                      </div>
                    )}

                    {bookingSettings?.show_prices !== false && (
                      <div>
                        <p className="text-sm text-gray-500">Price</p>
                        <p className="font-medium">{formatPrice(getSelectedService()?.price || 0)}</p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-md mb-6">
                  <h3 className="font-medium text-gray-800 mb-2">Your Information</h3>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Name</p>
                      <p className="font-medium">
                        {clientInfo.firstName} {clientInfo.lastName}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{clientInfo.email}</p>
                    </div>

                    {clientInfo.phone && (
                      <div>
                        <p className="text-sm text-gray-500">Phone</p>
                        <p className="font-medium">{clientInfo.phone}</p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-6 flex justify-between">
                  <button
                    type="button"
                    onClick={() => setStep(2)}
                    className="bg-gray-200 text-gray-800 px-6 py-2 rounded-md hover:bg-gray-300"
                  >
                    Back
                  </button>
                  <button
                    type="button"
                    onClick={handleSubmit}
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Booking...
                      </span>
                    ) : (
                      'Confirm Booking'
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default BookingPage;
