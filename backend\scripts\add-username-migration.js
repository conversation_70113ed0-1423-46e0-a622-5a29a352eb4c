const { supabase } = require('../dist/config/supabase');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  try {
    console.log('Running username migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../../supabase/migrations/04_add_username_to_tenants.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        // For DO blocks and complex statements, we need to use rpc
        if (statement.trim().startsWith('DO $$')) {
          console.log('Executing DO block via rpc...');
          const { error } = await supabase.rpc('execute_sql', { sql_query: statement });
          if (error) {
            console.error('Error executing DO block:', error);
            throw error;
          }
        } else {
          // For simple statements, we can use the direct query
          const { error } = await supabase.from('_sql').select('*').eq('query', statement);
          if (error && !error.message.includes('relation "_sql" does not exist')) {
            console.error('Error executing statement:', error);
            throw error;
          }
        }
        
        console.log(`Statement ${i + 1} executed successfully`);
      } catch (err) {
        console.error(`Error executing statement ${i + 1}:`, err.message);
        // Continue with other statements
      }
    }
    
    console.log('Migration completed!');
    
    // Test the migration by checking if username column exists
    const { data: tenants, error } = await supabase
      .from('tenants')
      .select('id, name, username')
      .limit(5);
    
    if (error) {
      console.error('Error testing migration:', error);
    } else {
      console.log('Migration test successful. Sample tenants:');
      console.log(tenants);
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

runMigration();
