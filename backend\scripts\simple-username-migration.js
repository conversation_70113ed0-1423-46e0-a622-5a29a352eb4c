const { supabase } = require('../dist/config/supabase');

async function addUsernameColumn() {
  try {
    console.log('Adding username column to tenants table...');
    
    // First, let's check if the column already exists
    const { data: existingTenants, error: checkError } = await supabase
      .from('tenants')
      .select('id, name')
      .limit(1);
    
    if (checkError) {
      console.error('Error checking tenants table:', checkError);
      return;
    }
    
    console.log('Tenants table exists. Checking if username column exists...');
    
    // Try to select username column to see if it exists
    const { data: usernameCheck, error: usernameError } = await supabase
      .from('tenants')
      .select('username')
      .limit(1);
    
    if (usernameError && usernameError.message.includes('column tenants.username does not exist')) {
      console.log('Username column does not exist. This is expected for the first run.');
      console.log('Please manually add the username column to your Supabase database:');
      console.log('');
      console.log('1. Go to your Supabase dashboard');
      console.log('2. Navigate to Table Editor > tenants');
      console.log('3. Add a new column:');
      console.log('   - Name: username');
      console.log('   - Type: text');
      console.log('   - Default value: (leave empty)');
      console.log('   - Is nullable: true');
      console.log('   - Is unique: true');
      console.log('');
      console.log('Or run this SQL in the SQL Editor:');
      console.log('ALTER TABLE tenants ADD COLUMN username TEXT UNIQUE;');
      console.log('CREATE INDEX IF NOT EXISTS idx_tenants_username ON tenants(username);');
    } else if (usernameError) {
      console.error('Unexpected error checking username column:', usernameError);
    } else {
      console.log('Username column already exists!');
      console.log('Sample data:', usernameCheck);
    }
    
    // Show current tenants
    console.log('\nCurrent tenants in database:');
    const { data: allTenants, error: tenantsError } = await supabase
      .from('tenants')
      .select('id, name, email')
      .limit(10);
    
    if (tenantsError) {
      console.error('Error fetching tenants:', tenantsError);
    } else {
      console.log(allTenants);
    }
    
  } catch (error) {
    console.error('Script failed:', error);
  }
}

addUsernameColumn();
